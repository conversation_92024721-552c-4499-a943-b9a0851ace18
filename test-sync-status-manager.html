<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>同步状态管理器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f1f1f1;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a87;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status-display {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .notice {
            padding: 12px;
            border-left: 4px solid #0073aa;
            background: #f0f8ff;
            margin: 10px 0;
        }
        .notice.error {
            border-left-color: #dc3232;
            background: #fff0f0;
        }
        .notice.success {
            border-left-color: #46b450;
            background: #f0fff0;
        }
        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #0073aa;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 5px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .log-entry {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .log-time {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>🔄 同步状态管理器测试</h1>
    
    <div class="test-container">
        <h2>📱 页面可见性测试</h2>
        <p>切换到其他标签页，然后回来查看状态变化：</p>
        <div id="visibility-status" class="status-display">页面当前状态：可见</div>
        <button class="button" onclick="toggleVisibilityMonitoring()">开始/停止监控</button>
    </div>
    
    <div class="test-container">
        <h2>💾 状态持久化测试</h2>
        <p>模拟同步操作，然后刷新页面查看状态恢复：</p>
        <button class="button" onclick="simulateSync('智能同步')">模拟智能同步</button>
        <button class="button" onclick="simulateSync('完全同步')">模拟完全同步</button>
        <button class="button" onclick="clearSyncStatus()">清除状态</button>
        <button class="button" onclick="location.reload()">刷新页面</button>
        <div id="sync-status" class="status-display">当前同步状态：无</div>
    </div>
    
    <div class="test-container">
        <h2>⏱️ 状态监控测试</h2>
        <p>查看定期状态检查的工作情况：</p>
        <button class="button" onclick="startStatusMonitoring()">开始监控</button>
        <button class="button" onclick="stopStatusMonitoring()">停止监控</button>
        <div id="monitoring-log" class="status-display">监控日志：\n</div>
    </div>
    
    <div class="test-container">
        <h2>🔍 状态检查测试</h2>
        <p>手动触发状态检查：</p>
        <button class="button" onclick="checkSyncStatus()">检查状态</button>
        <button class="button" onclick="showRecoveryNotice()">显示恢复提示</button>
        <div id="check-result" class="status-display">检查结果：等待操作</div>
    </div>

    <script>
        // 模拟同步状态管理器
        const TestSyncStatusManager = {
            STORAGE_KEY: 'notion_wp_sync_status',
            CHECK_INTERVAL_VISIBLE: 5000,
            CHECK_INTERVAL_HIDDEN: 30000,
            
            checkTimer: null,
            isPageVisible: true,
            currentSyncId: null,
            monitoringActive: false,
            
            init: function() {
                this.setupVisibilityHandling();
                this.restoreSyncStatus();
                this.log('🔄 同步状态管理器已初始化');
            },
            
            setupVisibilityHandling: function() {
                const self = this;
                
                document.addEventListener('visibilitychange', function() {
                    self.isPageVisible = !document.hidden;
                    const status = self.isPageVisible ? '可见' : '隐藏';
                    
                    document.getElementById('visibility-status').textContent = 
                        `页面当前状态：${status}\n检查间隔：${self.isPageVisible ? '5秒' : '30秒'}`;
                    
                    self.log(`📱 页面${status}，调整检查间隔`);
                    
                    if (self.monitoringActive) {
                        self.adjustCheckInterval();
                    }
                });
                
                window.addEventListener('focus', function() {
                    self.log('🎯 页面重新获得焦点');
                });
            },
            
            saveSyncStatus: function(syncData) {
                const statusData = {
                    isActive: true,
                    syncType: syncData.syncType || 'unknown',
                    startTime: Date.now(),
                    syncId: this.generateSyncId(),
                    ...syncData
                };
                
                this.currentSyncId = statusData.syncId;
                localStorage.setItem(this.STORAGE_KEY, JSON.stringify(statusData));
                
                this.updateSyncStatusDisplay(statusData);
                this.log(`💾 同步状态已保存: ${statusData.syncType}`);
            },
            
            clearSyncStatus: function() {
                localStorage.removeItem(this.STORAGE_KEY);
                this.currentSyncId = null;
                
                this.updateSyncStatusDisplay(null);
                this.log('🗑️ 同步状态已清除');
            },
            
            restoreSyncStatus: function() {
                const savedStatus = localStorage.getItem(this.STORAGE_KEY);
                
                if (savedStatus) {
                    try {
                        const statusData = JSON.parse(savedStatus);
                        const elapsed = Date.now() - statusData.startTime;
                        
                        if (elapsed > 3600000) { // 1小时
                            this.clearSyncStatus();
                            return;
                        }
                        
                        this.currentSyncId = statusData.syncId;
                        this.updateSyncStatusDisplay(statusData);
                        this.showRecoveryNotice(statusData);
                        this.log(`🔄 恢复同步状态: ${statusData.syncType}`);
                        
                    } catch (e) {
                        this.log(`❌ 解析保存状态失败: ${e.message}`);
                        this.clearSyncStatus();
                    }
                }
            },
            
            updateSyncStatusDisplay: function(statusData) {
                const display = document.getElementById('sync-status');
                
                if (statusData) {
                    const elapsed = Math.floor((Date.now() - statusData.startTime) / 1000);
                    const elapsedText = elapsed < 60 ? `${elapsed}秒` : `${Math.floor(elapsed / 60)}分${elapsed % 60}秒`;
                    
                    display.textContent = `当前同步状态：${statusData.syncType}\n` +
                                        `同步ID：${statusData.syncId}\n` +
                                        `开始时间：${new Date(statusData.startTime).toLocaleTimeString()}\n` +
                                        `已运行：${elapsedText}`;
                } else {
                    display.textContent = '当前同步状态：无';
                }
            },
            
            generateSyncId: function() {
                return 'sync_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            },
            
            adjustCheckInterval: function() {
                if (this.checkTimer) {
                    clearInterval(this.checkTimer);
                }
                
                const interval = this.isPageVisible ? this.CHECK_INTERVAL_VISIBLE : this.CHECK_INTERVAL_HIDDEN;
                
                this.checkTimer = setInterval(() => {
                    this.checkSyncStatus();
                }, interval);
                
                this.log(`⏱️ 检查间隔调整为 ${interval/1000}秒`);
            },
            
            startStatusMonitoring: function() {
                this.monitoringActive = true;
                this.adjustCheckInterval();
                this.log('▶️ 开始状态监控');
            },
            
            stopStatusMonitoring: function() {
                this.monitoringActive = false;
                if (this.checkTimer) {
                    clearInterval(this.checkTimer);
                    this.checkTimer = null;
                }
                this.log('⏹️ 停止状态监控');
            },
            
            checkSyncStatus: function() {
                this.log('🔍 检查同步状态...');
                
                // 模拟AJAX检查
                setTimeout(() => {
                    const result = Math.random() > 0.7 ? '同步完成' : '同步进行中';
                    this.log(`📊 状态检查结果: ${result}`);
                    
                    document.getElementById('check-result').textContent = 
                        `检查结果：${result}\n检查时间：${new Date().toLocaleTimeString()}`;
                    
                    if (result === '同步完成' && this.currentSyncId) {
                        this.clearSyncStatus();
                    }
                }, 1000);
            },
            
            showRecoveryNotice: function(statusData) {
                if (!statusData) {
                    statusData = JSON.parse(localStorage.getItem(this.STORAGE_KEY) || '{}');
                }
                
                if (!statusData.syncType) return;
                
                const elapsed = Math.floor((Date.now() - statusData.startTime) / 1000);
                const elapsedText = elapsed < 60 ? `${elapsed}秒` : `${Math.floor(elapsed / 60)}分${elapsed % 60}秒`;
                
                const notice = document.createElement('div');
                notice.className = 'notice';
                notice.innerHTML = `
                    <strong>🔄 检测到进行中的同步操作</strong><br>
                    同步类型：${statusData.syncType}<br>
                    已运行：${elapsedText}<br>
                    <button class="button" onclick="TestSyncStatusManager.checkSyncStatus()">立即检查状态</button>
                    <button class="button" onclick="TestSyncStatusManager.clearSyncStatus(); this.parentElement.remove()">清除状态</button>
                `;
                
                document.body.insertBefore(notice, document.body.firstChild);
                
                setTimeout(() => {
                    if (notice.parentElement) {
                        notice.remove();
                    }
                }, 10000);
            },
            
            log: function(message) {
                const logElement = document.getElementById('monitoring-log');
                const time = new Date().toLocaleTimeString();
                const entry = `[${time}] ${message}\n`;
                
                logElement.textContent += entry;
                logElement.scrollTop = logElement.scrollHeight;
                
                console.log(`[SyncStatusManager] ${message}`);
            }
        };
        
        // 测试函数
        function simulateSync(syncType) {
            TestSyncStatusManager.saveSyncStatus({
                syncType: syncType,
                incremental: syncType === '智能同步',
                buttonId: 'test-button'
            });
        }
        
        function clearSyncStatus() {
            TestSyncStatusManager.clearSyncStatus();
        }
        
        function checkSyncStatus() {
            TestSyncStatusManager.checkSyncStatus();
        }
        
        function startStatusMonitoring() {
            TestSyncStatusManager.startStatusMonitoring();
        }
        
        function stopStatusMonitoring() {
            TestSyncStatusManager.stopStatusMonitoring();
        }
        
        function showRecoveryNotice() {
            TestSyncStatusManager.showRecoveryNotice();
        }
        
        let visibilityMonitoring = false;
        function toggleVisibilityMonitoring() {
            visibilityMonitoring = !visibilityMonitoring;
            
            if (visibilityMonitoring) {
                TestSyncStatusManager.log('👁️ 开始页面可见性监控');
            } else {
                TestSyncStatusManager.log('👁️ 停止页面可见性监控');
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            TestSyncStatusManager.init();
        });
    </script>
</body>
</html>
