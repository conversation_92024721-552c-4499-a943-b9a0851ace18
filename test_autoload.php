<?php
/**
 * Test script to verify PSR-4 autoloading works correctly
 */

// Load composer autoloader
$autoloader_path = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoloader_path)) {
    require_once $autoloader_path;
    echo "✓ Composer autoloader loaded successfully\n";
} else {
    echo "✗ Composer autoloader not found\n";
    exit(1);
}

// Test PSR-4 namespace loading
$test_classes = [
    'NTWP\\Core\\Logger',
    'NTWP\\Utils\\Concurrent_Network_Manager',
    'NTWP\\Framework\\Main',
    'NTWP\\Services\\API_Service',
    'NTWP\\Handlers\\Import_Coordinator'
];

echo "\nTesting PSR-4 autoloading:\n";
foreach ($test_classes as $class) {
    if (class_exists($class)) {
        echo "✓ {$class} - autoloaded successfully\n";
    } else {
        echo "✗ {$class} - failed to autoload\n";
    }
}

echo "\nAll tests completed!\n";